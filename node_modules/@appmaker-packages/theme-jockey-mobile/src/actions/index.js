
import { appmaker } from '@appmaker-xyz/core';
import * as checkoutActions from './checkout';
import * as getCheckoutUrlActions from './getCheckoutUrl';

function MY_CUSTOM_ACTION(
  { action, params },
  { handleAction, appStorageState },
) {
  // you can write the logic for your action here
  alert('My custom action is called');
}

function MY_SECOND_ACTION(
  { action, params },
  { handleAction, appStorageState },
) {
  // You can also call another action from using handleAction
  handleAction({
    action: 'MY_CUSTOM_ACTION',
    params: {
      key: 'value',
    },
  });
}

export function registerActions() {
  // Register default actions
  appmaker.actions.registerAction('MY_CUSTOM_ACTION', MY_CUSTOM_ACTION);
  appmaker.actions.registerAction('MY_SECOND_ACTION', MY_SECOND_ACTION);

  // Register custom checkout actions
  appmaker.actions.registerAction('START_CHECKOUT', checkoutActions.START_CHECKOUT);
  appmaker.actions.registerAction('GET_CHECKOUT_URL', getCheckoutUrlActions.GET_CHECKOUT_URL);

  console.log('Custom actions registered successfully');
}