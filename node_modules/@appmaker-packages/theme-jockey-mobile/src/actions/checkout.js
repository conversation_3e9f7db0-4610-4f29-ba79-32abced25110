import { appStorageApi } from '@appmaker-xyz/core';
import { isShopifyCheckoutSheetEnabled } from '@appmaker-xyz/shopify/helper/helper';
import { shopifyCheckout } from '@appmaker-xyz/shopify/souces/checkout';
import { getCheckoutUrl, getCheckoutData } from '../utils/checkoutUtils';

// Custom checkout action handler with enhanced logging and error handling
export async function START_CHECKOUT({ params }, dependencies) {
  const { handleAction } = dependencies;
  const { user } = appStorageApi()?.getState() || {};
  const checkoutUrl = getCheckoutUrl();
  const checkoutData = getCheckoutData();

  // Log the checkout process with detailed information
  console.log('[START_CHECKOUT] Starting checkout process');
  console.log('[START_CHECKOUT] Checkout URL:', checkoutUrl);

  // Log detailed checkout payload
  console.log('[START_CHECKOUT] Checkout Data:', JSON.stringify(checkoutData, null, 2));

  // Log request headers that will be sent
  console.log('[START_CHECKOUT] Request Headers:', JSON.stringify({
    'X-Shopify-Customer-Access-Token': user?.accessToken ? 'Present (not shown for security)' : 'Not present',
  }, null, 2));

  try {
    // Check if we have a valid checkout URL
    if (!checkoutUrl) {
      console.error('[START_CHECKOUT] No checkout URL available');
      handleAction({
        action: 'SHOW_MESSAGE',
        params: {
          title: 'Checkout Error',
          message: 'Unable to start checkout. Please try again.'
        },
      });
      return;
    }

    // Determine if we should use native checkout sheet or webview
    if (isShopifyCheckoutSheetEnabled()) {
      console.log('[START_CHECKOUT] Using Shopify Checkout Sheet');
      try {
        return shopifyCheckout.presentCheckout(checkoutUrl);
      } catch (error) {
        console.error('[START_CHECKOUT] Error with Shopify Checkout Sheet:', error);
        // Fall back to webview if checkout sheet fails
        console.log('[START_CHECKOUT] Falling back to webview checkout');
      }
    }

    // Use custom checkout webview page for better handling
    console.log('[START_CHECKOUT] Using custom checkout webview');
    return handleAction({
      action: 'OPEN_INAPP_PAGE',
      pageId: 'CheckoutPaymentWebview',
      params: {
        replacePage: !!params?.replacePage,
      },
      pageData: {
        source: user?.accessToken
          ? {
              uri: checkoutUrl,
              headers: {
                'X-Shopify-Customer-Access-Token': user.accessToken,
              },
            }
          : { uri: checkoutUrl },
      },
    });
  } catch (error) {
    console.error('[START_CHECKOUT] Error during checkout:', error);
    handleAction({
      action: 'SHOW_MESSAGE',
      params: {
        title: 'Checkout Error',
        message: 'An error occurred during checkout. Please try again.'
      },
    });
  }
}
