import { getCheckoutUrl, getCheckoutData } from '../utils/checkoutUtils';

/**
 * Action handler to get the checkout URL
 * 
 * This function can be called from anywhere in the application to get the current checkout URL.
 * It returns the URL directly without any side effects.
 * 
 * @param {Object} action - The action object
 * @param {Object} dependencies - The dependencies object
 * @returns {Object} An object containing the checkout URL and data
 */
export async function GET_CHECKOUT_URL(action, dependencies) {
  const checkoutUrl = getCheckoutUrl();
  const checkoutData = getCheckoutData();
  
  console.log('[GET_CHECKOUT_URL] Checkout URL:', checkoutUrl);
  
  return {
    checkoutUrl,
    checkoutData
  };
}
