import React from 'react';
import { StyleSheet } from 'react-native';
import { shopConfig } from '@appmaker-xyz/shopify/store/shopifyStore';
import {
  getShopifyBaseUrl,
  isOrderCompleted,
  webviewDeepLinkEnabled,
} from '@appmaker-xyz/shopify/helper/helper';
import { getActionFromURL } from '@appmaker-xyz/shopify/helper/deeplink';

// Enhanced URL change handler with better logging and navigation handling
const onUrlChange = async (url, onAction) => {
  console.log('[CheckoutWebview] URL changed to:', url);

  // Log detailed information about the URL and any query parameters
  try {
    const urlObj = new URL(url);
    console.log('[CheckoutWebview] URL Details:', {
      protocol: urlObj.protocol,
      hostname: urlObj.hostname,
      pathname: urlObj.pathname,
      search: urlObj.search,
      hash: urlObj.hash
    });

    // Parse and log query parameters
    const params = {};
    urlObj.searchParams.forEach((value, key) => {
      params[key] = value;
    });
    console.log('[CheckoutWebview] URL Query Parameters:', params);
  } catch (error) {
    console.log('[CheckoutWebview] Error parsing URL:', error.message);
  }

  const shopData = shopConfig.get();
  const homePageURL = shopData?.shop?.primaryDomain?.url;

  // Check if the URL is the order completion page
  if (isOrderCompleted(url)) {
    console.log('[CheckoutWebview] Order completed, redirecting to order complete page');
    onAction({
      action: 'SET_ORDER_COMPLETE',
    });
    return;
  }

  // Check for deep links
  if (webviewDeepLinkEnabled()) {
    const action = await getActionFromURL(url, {
      shopifyURL: getShopifyBaseUrl(),
      isWebView: true,
    });
    if (action) {
      console.log('[CheckoutWebview] Deep link detected, executing action:', action);
      return onAction(action);
    }
  }

  // Only redirect to home if we're sure it's the homepage and not part of the checkout flow
  // This helps prevent incorrect redirects during checkout
  const isMatch = url === homePageURL || url === `${homePageURL}/`;
  const isCheckoutUrl = url.includes('/checkouts/') ||
                        url.includes('/checkout') ||
                        url.includes('/cart') ||
                        url.includes('/account');

  // Add more logging to help diagnose the issue
  console.log('[CheckoutWebview] URL analysis:', {
    isMatch,
    isCheckoutUrl,
    homePageURL,
    currentUrl: url
  });

  // Only redirect to home if it's explicitly the homepage and not part of checkout flow
  // Add a longer delay to ensure it's not a temporary redirect
  if (isMatch && !isCheckoutUrl) {
    console.log('[CheckoutWebview] Homepage detected, but not redirecting immediately');
    // Add a longer delay to ensure it's not a temporary redirect
    setTimeout(() => {
      console.log('[CheckoutWebview] Confirming homepage redirect');
      onAction({
        action: 'GO_TO_HOME',
      });
    }, 1000); // Increased delay to 1 second
  }
};

// Log the source object when the component mounts
const onComponentMount = (props) => {
  console.log('[CheckoutWebview] Component mounted with source:', JSON.stringify(props.source, null, 2));
  return null; // Return null as this is just for logging
};

// Define the page configuration
const page = {
  id: 'checkout',
  status: 'active',
  title: 'Checkout',
  attributes: {
    renderType: 'normal',
    contentContainerStyle: { flex: 1 },
    rootContainerStyle: { flex: 1 },
  },
  blocks: [
    {
      name: 'appmaker/shopify-payment-webview',
      isValid: true,
      clientId: 'f496b61a-56c9-4862-b4a5-d5438bb530aa',
      attributes: {
        loadingLayout: 'home',
        urlListener: onUrlChange,
        source: '{{blockData.source}}',
        // Add additional configuration to improve webview stability
        cacheEnabled: true,
        domStorageEnabled: true,
        javaScriptEnabled: true,
        thirdPartyCookiesEnabled: true,
        sharedCookiesEnabled: true,
        onComponentMount: onComponentMount,
      },
      dependencies: {
        appStorageState: ['checkout'],
      },
    },
  ],
};

export default page;
