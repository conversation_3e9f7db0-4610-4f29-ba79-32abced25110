import React, { useState } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Modal,
  Text,
  StyleSheet,
  Dimensions,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import {
  useProductDetail,
  useProductImages,
  useProductWishList,
  useUser
} from '@appmaker-xyz/shopify';
import SwiperFlatList from 'react-native-swiper-flatlist';
import Icon from 'react-native-vector-icons/FontAwesome';
import Svg, { Circle, Path } from 'react-native-svg';
import ImageViewer from 'react-native-image-zoom-viewer';
import FastImage from 'react-native-fast-image';
import { fonts, heightPixel, widthPixel } from '../../styles';
import { useNavigation } from '@react-navigation/native';
import { getIdFromGID, updateWebWishlist } from '../../utils/Helper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import ShopifyImage from '../ShopifyImage';

export default function ProductImage(props) {
  const [isModalVisible, setModalVisible] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [currentModalIndex, setCurrentModalIndex] = useState(0);
  const { imageList } = useProductImages(props);
  const { product, shareProduct } = useProductDetail(props);
  const { toggleWishList, isSaved } = useProductWishList(props);
  const insets = useSafeAreaInsets();
  const { user } = useUser();

  const customerId = getIdFromGID(user?.id)

  const navigation = useNavigation();



  const updateWishlist = () => {
    toggleWishList();
    if (!customerId) return
    const productId = getIdFromGID(product?.node?.id);
    const variantId = getIdFromGID(product?.node?.variants?.edges[0]?.node?.id);
    const action = isSaved ? "remove" : "add";
    updateWebWishlist(customerId, productId, variantId, action)
  }

  const handleImagePress = (index) => {
    setCurrentIndex(index);
    setCurrentModalIndex(index);
    setModalVisible(true);
  };

  const renderCustomImage = (image) => {
    return (
      <ShopifyImage
        style={[image.style, { margin: widthPixel(3) }]}
        source={{
          uri: image.source.uri,
        }}
        maxWidth={800}
        resizeMode={FastImage.resizeMode.contain}
      />
    );
  };

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          height: heightPixel(500),
          overflow: 'hidden',
          backgroundColor: 'white',
          width: '100%',
        }}>
        <SwiperFlatList
          showPagination
          autoplay
          autoplayDelay={3}
          autoplayLoop
          renderAll
          paginationStyle={{
            position: 'absolute',
            bottom: 0,
            alignSelf: 'center',
            flexDirection: 'row',
            alignItems: 'center',
          }}
          paginationStyleItemActive={{
            width: widthPixel(20),
            height: heightPixel(5),
            borderRadius: widthPixel(5),
            marginHorizontal: widthPixel(5),
          }}
          paginationStyleItem={{
            width: widthPixel(7),
            height: widthPixel(7),
            borderRadius: widthPixel(5),
            marginHorizontal: widthPixel(4),
          }}
          paginationActiveColor="black"
          paginationDefaultColor="#dfdfdf"
          data={imageList}
          onChangeIndex={({ index }) => {
            if (!isModalVisible) {
              setCurrentIndex(index);
            }
          }}
          nextButton={
            <View style={styles.arrowContainerRight}>
              <TouchableOpacity
                onPress={() => {
                  setCurrentIndex((prevIndex) =>
                    prevIndex < imageList.length - 1
                      ? prevIndex + 1
                      : prevIndex,
                  );
                }}>
                <Icon name="arrow-right" size={30} color="white" />
              </TouchableOpacity>
            </View>
          }
          prevButton={
            <View style={styles.arrowContainerLeft}>
              <TouchableOpacity
                onPress={() => {
                  setCurrentIndex((prevIndex) =>
                    prevIndex > 0 ? prevIndex - 1 : prevIndex,
                  );
                }}>
                <Icon name="arrow-left" size={30} color="white" />
              </TouchableOpacity>
            </View>
          }
          renderItem={({ item, index }) => (
            <Pressable key={index} onPress={() => handleImagePress(index)}>
              <View
                style={{
                  width: Dimensions.get('window').width,
                  height: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <ShopifyImage
                  source={{
                    uri: item.uri,
                  }}
                  maxWidth={800}
                  style={{ width: '100%', height: '100%' }}
                  resizeMode={FastImage.resizeMode.cover}
                />
              </View>

              <View
                style={{
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  backgroundColor: '#0000004d',
                  fontSize: fonts._19,
                  borderWidth: 0,
                  borderColor: 'black',
                  paddingHorizontal: widthPixel(10),
                  paddingVertical: heightPixel(6),
                  borderTopLeftRadius: widthPixel(10),
                  borderBottomRightRadius: widthPixel(10),
                }}
              >
                <Text style={styles.styleText}>
                  Style: {product?.node?.style_number?.value}
                </Text>
              </View>

            </Pressable>
          )}
        />
        {/* Icon container */}
        <View style={styles.iconContainer}>
          <Pressable
            style={styles.wishlistIcon}
            onPress={updateWishlist}>
            {isSaved ? (
              <Icon name="heart" size={23} color={'white'} />
            ) : (
              <Icon name="heart-o" size={23} color={'white'} />
            )}
          </Pressable>
          <Pressable onPress={() => shareProduct()}>
            <Svg width="40" height="40" viewBox="0 0 40 40" fill="none">
              <Circle cx="20" cy="20" r="20" fill="black" fillOpacity="0.11" />
              <Path
                d="M16.25 21.375L21.75 24.125M21.75 15.875L16.25 18.625M24.5 28.25C22.9812 28.25 21.75 27.0188 21.75 25.5C21.75 23.9812 22.9812 22.75 24.5 22.75C26.0188 22.75 27.25 23.9812 27.25 25.5C27.25 27.0188 26.0188 28.25 24.5 28.25ZM13.5 22.75C11.9812 22.75 10.75 21.5188 10.75 20C10.75 18.4812 11.9812 17.25 13.5 17.25C15.0188 17.25 16.25 18.4812 16.25 20C16.25 21.5188 15.0188 22.75 13.5 22.75ZM24.5 17.25C22.9812 17.25 21.75 16.0188 21.75 14.5C21.75 12.9812 22.9812 11.75 24.5 11.75C26.0188 11.75 27.25 12.9812 27.25 14.5C27.25 16.0188 26.0188 17.25 24.5 17.25Z"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </Svg>
          </Pressable>
        </View>
      </View>

      {/* Full-screen modal */}
      <Modal
        visible={isModalVisible}
        transparent={true}
        onRequestClose={() => setModalVisible(false)}>
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setModalVisible(false)}>
          {/* Modal close */}
          <View style={[styles.modalHeader, { top: 10 + insets.top }]}>
            <Pressable
              onPress={() => setModalVisible(false)}
            >
              <Svg
                width={30}
                height={26}
                viewBox="0 0 16 16"
                fill="none"
                style={{ backgroundColor: '#000' }}>
                <Path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M8 8.707L11.646 12.354l.708-.707L8.707 8l3.647-3.646-.707-.708L8 7.293 4.354 3.646l-.707.708L7.293 8l-3.646 3.646.707.708L8 8.707z"
                  fill="#fff"
                />
              </Svg>
            </Pressable>
          </View>
          {/* Modal back and forward buttons */}
          <View style={styles.modalNavContainer}>
            <Pressable
              onPress={() => {
                if (currentModalIndex > 0) {
                  setCurrentModalIndex((prevIndex) => prevIndex - 1);
                }
              }}
              // disabled={currentModalIndex == 0 ? true : false}
              style={[
                styles.modalNavButtonLeft,
                { opacity: currentModalIndex == 0 ? 0.35 : 1 },
              ]}>
              <Svg width={30} height={26} viewBox="0 0 36 36">
                <Path
                  d="M27.66 15.61 18 6l-9.66 9.61A1 1 0 1 0 9.75 17L17 9.81v19.13a1 1 0 1 0 2 0V9.81L26.25 17a1 1 0 0 0 1.41-1.42Z"
                  fill="#fff"
                  stroke="#fff"
                />
                <Path fill="none" d="M0 0h36v36H0z" />
              </Svg>
            </Pressable>
            <Pressable
              onPress={() => {
                if (currentModalIndex < imageList.length - 1) {
                  setCurrentModalIndex((prevIndex) => prevIndex + 1);
                }
              }}
              // disabled={currentModalIndex == imageList.length - 1 ? true : false}
              style={[
                styles.modalNavButtonRight,
                {
                  opacity: currentModalIndex == imageList.length - 1 ? 0.35 : 1,
                },
              ]}>
              <Svg width={30} height={26} viewBox="0 0 36 36">
                <Path
                  d="M27.66 15.61 18 6l-9.66 9.61A1 1 0 1 0 9.75 17L17 9.81v19.13a1 1 0 1 0 2 0V9.81L26.25 17a1 1 0 0 0 1.41-1.42Z"
                  fill="#fff"
                  stroke="#fff"
                />
                <Path fill="none" d="M0 0h36v36H0z" />
              </Svg>
            </Pressable>
          </View>
          <ImageViewer
            backgroundColor="transparent"
            // imageUrls={imageList.map((item) => ({ url: item.uri }))}
            imageUrls={imageList.map((item) => ({
              url: item.uri,
              props: { source: { uri: item.uri, cache: 'force-cache' } }, // Use FastImage caching
            }))}
            index={currentModalIndex}
            enableSwipeDown={false} // Disable swipe-down gesture
            renderIndicator={(currentIndex, allSize) => null}
            onChange={(index) => {
              setCurrentModalIndex(index);
            }}
            renderImage={renderCustomImage}
            enablePreload={true}
            onMove={() => { }} // Disable drag interactions
            loadingRender={() => (
              <ActivityIndicator size={'large'} color="#fff" />
            )}
          />
        </Pressable>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  arrowContainerRight: {
    position: 'absolute',
    right: widthPixel(10),
    top: '50%',
    transform: [{ translateY: -15 }],
    zIndex: 1,
  },
  arrowContainerLeft: {
    position: 'absolute',
    left: widthPixel(10),
    top: '50%',
    transform: [{ translateY: -15 }],
    zIndex: 1,
  },
  iconContainer: {
    position: 'absolute',
    top: heightPixel(20),
    right: widthPixel(20),
    flexDirection: 'column',
    alignItems: 'center',
  },
  wishlistIcon: {
    // marginRight: 10,
    backgroundColor: '#0000001C',
    padding: widthPixel(8),
    // marginRight: 5,
    borderRadius: widthPixel(40),
    marginBottom: heightPixel(15),
  },
  backButton: {
    position: 'absolute',
    top: heightPixel(20),
    left: widthPixel(20),
  },
  styleTag: {
    position: 'absolute',
    bottom: heightPixel(20),
    left: widthPixel(20),
    backgroundColor: 'black',
    padding: widthPixel(10),
    borderRadius: widthPixel(5),
  },
  styleText: {
    color: 'white',
    fontSize: fonts._14,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalHeader: {
    padding: widthPixel(10),
    alignItems: 'flex-end',
    position: 'absolute',
    right: 0,
    zIndex: 1
  },
  modalNavContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    top: '50%',
    width: '100%',
    zIndex: 1,
  },
  modalNavButtonLeft: {
    marginLeft: widthPixel(5),
    backgroundColor: '#00000080',
    height: heightPixel(40),
    width: widthPixel(40),
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '-90deg' }],
  },
  modalNavButtonRight: {
    marginRight: widthPixel(5),
    height: heightPixel(40),
    width: widthPixel(40),
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#00000080',
    transform: [{ rotate: '90deg' }],
  },
});
