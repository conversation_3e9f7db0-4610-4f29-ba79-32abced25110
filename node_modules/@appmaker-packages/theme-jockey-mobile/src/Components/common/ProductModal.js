import React, { useEffect, useState } from 'react';
import {
  Alert,
  Animated,
  Easing,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  Dimensions,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import { Modal } from 'react-native';
import { View } from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import Svg, { Path, Rect } from 'react-native-svg';
import LinearGradient from 'react-native-linear-gradient';
import { useRef } from 'react';
import { useLocalCart, useCartActions } from '@appmaker-xyz/shopify';
import { fonts, heightPixel, widthPixel } from '../../styles';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import FastImage from 'react-native-fast-image';
import { usePageState } from '@appmaker-xyz/core';
import ShopifyImage from '../ShopifyImage';
import Snackbar from 'react-native-snackbar';
import { showSnackbarWithIcon } from '../../utils/Helper';

const { height, width } = Dimensions.get('window');

function fontSizerLarge(screenWidth) {
  if (screenWidth > 400) {
    return 14;
  } else if (screenWidth > 250) {
    return 13;
  } else {
    return 12;
  }
}

function qtyFontSize(screenWidth) {
  if (screenWidth > 400) {
    return 24;
  } else if (screenWidth > 250) {
    return 22;
  } else {
    return 20;
  }
}

function qtyIcon(screenWidth) {
  if (screenWidth > 400) {
    return 12;
  } else if (screenWidth > 250) {
    return 10;
  } else {
    return 8;
  }
}

function fontSizerMedium(screenWidth) {
  if (screenWidth > 400) {
    return 12;
  } else if (screenWidth > 250) {
    return 12;
  } else {
    return 10;
  }
}

function closeIcon(screenWidth) {
  if (screenWidth > 400) {
    return 35;
  } else if (screenWidth > 250) {
    return 30;
  } else {
    return 25;
  }
}

export default function ProductModal({
  modalVisible,
  toggleModal,
  props,
  productVariantDetails,
  onSuccess, isFromCart = false, addToCart
}) {
  const [fetchProducts, setFetchProducts] = useState({
    result: {},
    isLoading: false,
    error: false,
  });
  const setPageState = usePageState((state) => state.setPageState);
  const { manageCart, cartActionLoading } = useCartActions({});
  const insets = useSafeAreaInsets();

  const [allProductVariantDetails, setAllProductVariantDetails] = useState([]);
  const [selectedSize, setSelectedSize] = useState(null);
  const [selectedProductHandle, setSelectedProductHandle] = useState('');
  const [selectedImage, setSelectedImage] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [isAtbBtnLoading, setIsAtbBtnLoading] = useState(true);
  const [loading, setLoader] = useState(false);

  const [isAddedATB, setIsAddedATB] = useState(false);
  const [isUpdateATB, setIsUpdateATB] = useState(false);
  const [selectedCartLineItemId, setSelectedCartLineItemId] = useState('');
  const [cartQuantity, setCartQuantity] = useState(1);
  const [warningMessage, setWariningMessage] = useState({
    isVisible: false,
    msg: '',
  });
  const [finalSelectedProduct, setFinalSelectedProduct] = useState({
    productId: '',
    variantId: '',
    availableQty: null,
    qty: 1,
    size: '',
  });
  const { cart } = useLocalCart();

  const cartData = cart?.lineItems?.edges;

  // Animation values
  const translateX = useRef(new Animated.Value(0)).current; // For cart icon moving left to center
  const dropY = useRef(new Animated.Value(-50)).current; // For rectangle box dropping from top
  const opacity = useRef(new Animated.Value(0)).current; // For fading in the rectangle box
  const moveRight = useRef(new Animated.Value(0)).current; // For both elements moving right
  const addedTextTranslateX = useRef(new Animated.Value(0)).current; // Start slightly off-screen
  const bottomSheetRef = useRef(null);

  const startAnimation = () => {
    if (
      !finalSelectedProduct?.availableQty ||
      !finalSelectedProduct?.variantId ||
      !finalSelectedProduct?.qty ||
      !finalSelectedProduct?.productId
    ) {
      setWariningMessage({
        isVisible: true,
        msg: 'Please Select a Size',
      });
      return;
    }
    setIsAtbBtnLoading(false);
    // First animation: Move cart icon from left to center
    const moveCartIcon = Animated.timing(translateX, {
      toValue: 200, // Change this value to move further or less to the center
      duration: 400, // Duration of the animation
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    });
    // Second animation: Drop rectangle box from top
    const dropBox = Animated.parallel([
      Animated.timing(dropY, {
        toValue: -11.45, // Final position in the middle of the cart icon
        duration: 300,
        delay: 500,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1, // Fade in the rectangle box
        duration: 100,
        useNativeDriver: true,
      }),
    ]);
    Animated.sequence([moveCartIcon, dropBox]).start(async () => {
      const result = await addToBagHandler();
      if (result) {
        moveRightAndHide();
      }
    });
  };

  const moveRightAndHide = () => {
    const moveRightAndHide = Animated.parallel([
      // Move the cart icon to the right
      Animated.timing(translateX, {
        toValue: 400, // Move right by 300 units
        duration: 400,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }),

      // Move the dropBox horizontally to the right
      Animated.timing(moveRight, {
        toValue: 180, // Move right by 300 units
        duration: 350,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true,
      }),
      // Fade out the dropBox
    ]);

    moveRightAndHide.start(() => {
      Animated.timing(addedTextTranslateX, {
        toValue: 400, // Final position in the center (adjust as needed)
        duration: 200,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start(() => {
        setTimeout(() => {
          toggleModal();
        }, 200);
      });
    });
  };

  const updateQuantity = (newQuantity) => {
    if (newQuantity >= 1) {
      setQuantity(newQuantity);
    }
  };

  // ### Select Variant Image Handler ### //
  const selectedVariantImageHandler = (id) => {
    setWariningMessage({
      isVisible: false,
      message: '',
    });
    setSelectedImage(id);
    setSelectedSize(null);
    setFinalSelectedProduct({
      productId: '',
      variantId: '',
      availableQty: null,
      qty: null,
      size: '',
    });
    setQuantity(1);
    setAllProductVariantDetails((prev) => {
      return prev?.map((variant) => {
        return {
          ...variant,
          isSelected: variant.productId === id,
        };
      });
    });
  };

  // ### Choose/Select Size Handler ### //
  const chooseSizeHandler = (selectedItem, index) => {
    setWariningMessage({
      isVisible: false,
      msg: '',
    });
    setSelectedSize(index);
    const checkCartAvailability = cartData?.find(
      (lineItem) => lineItem?.node?.variant?.id === selectedItem?.id,
    );
    if (!checkCartAvailability) {
      setIsAddedATB(false);
      setQuantity(1);
      setSelectedCartLineItemId('');
      setFinalSelectedProduct((prev) => {
        return {
          ...prev,
          productId: selectedItem?.productId,
          variantId: selectedItem?.id,
          availableQty: selectedItem?.qtyAvailable,
          size: selectedItem?.title,
          qty: 1,
        };
      });
    } else {
      setIsAddedATB(true);
      setSelectedCartLineItemId(checkCartAvailability?.node?.id);
      setCartQuantity(checkCartAvailability?.node?.quantity);
      setQuantity(checkCartAvailability?.node?.quantity);
      setFinalSelectedProduct((prev) => {
        return {
          ...prev,
          productId: selectedItem?.productId,
          variantId: selectedItem?.id,
          availableQty: selectedItem?.qtyAvailable,
          size: selectedItem?.title,
          qty: checkCartAvailability?.node?.quantity,
        };
      });
    }
  };

  // ### AddToBag Handler ## //
  const addToBagHandler = async () => {
    if (
      !finalSelectedProduct?.availableQty ||
      !finalSelectedProduct?.variantId ||
      !finalSelectedProduct?.qty ||
      !finalSelectedProduct?.productId
    ) {
      setWariningMessage({
        isVisible: true,
        msg: 'Please Select a Size',
      });
      return;
    }
    setIsAtbBtnLoading(false);
    if (isAddedATB && isUpdateATB && selectedCartLineItemId) {
      // Update Cart
      if (onSuccess) {
        setIsAtbBtnLoading(false);

        onSuccess([
          {
            variantId: finalSelectedProduct?.variantId,
            quantity: quantity,
            customAttributes: {
              key: 'jockey_mobile_app',
              value: 'true',
            },
          },
        ]);
        return true;
      }
      const result = await props?.onAction({
        action: 'UPDATE_CART_V2',
        params: {
          replacePage: true,
          lineItemId: selectedCartLineItemId,
          product: {
            node: {
              id: finalSelectedProduct?.productId,
            },
          },
          variant: {
            node: {
              id: finalSelectedProduct?.variantId,
            },
          },
          quantity: quantity,
          updateCartPageStateRequired: true,
          customAttributes: {
            key: 'jockey_mobile_app',
            value: 'true',
          },
        },
      });
      if (result?.status === 'success') {
        showSnackbarWithIcon("The product is updated in your bag.")
        setIsAtbBtnLoading(false);

        return true;
      } else {
        return false;
      }
    } else {
      if (onSuccess) {
        setIsAtbBtnLoading(false);

        onSuccess([
          {
            variantId: finalSelectedProduct?.variantId,
            quantity: quantity,
            customAttributes: {
              key: 'jockey_mobile_app',
              value: 'true',
            },
          },
        ]);
        return true;
      }

      const itemToAdd = [
        {
          variantId: finalSelectedProduct?.variantId,
          quantity: quantity,
          customAttributes: {
            key: 'jockey_mobile_app',
            value: 'true',
          },
        },
      ];

      if (addToCart) {
        addToCart({ buynow: false })
        return true;
      }
      showSnackbarWithIcon("The product is in your bag.")
      await manageCart({
        lineItemsToAdd: itemToAdd,
        updateCartPageStateRequired: isFromCart,
      });

      return true;
    }
  };

  const deleteFromCart = async () => {
    if (
      !finalSelectedProduct?.availableQty ||
      !finalSelectedProduct?.variantId ||
      !finalSelectedProduct?.qty ||
      !finalSelectedProduct?.productId
    ) {
      setWariningMessage({
        isVisible: true,
        msg: 'Please Select a Size',
      });
      return;
    } else {
      if (selectedCartLineItemId) {
        setLoader(true);
        await manageCart({
          lineItemsToRemove: [selectedCartLineItemId],
          updateCartPageStateRequired: true,
        });
        toggleModal();
        setLoader(false);

        return true;
      } else {
        Alert.alert(
          'Error',
          'Something went wrong while deleting Selected product variant',
        );
      }
    }
  };

  useEffect(() => {
    // ## API call for poduct using ids ## //
    // fetchProductsById();
    if (productVariantDetails?.nodes?.length > 0) {
      setSelectedProductHandle(productVariantDetails?.nodes[0]?.handle);
      setFetchProducts({
        result: productVariantDetails?.nodes?.filter((variant) => variant),
        isLoading: true,
        error: false,
      });
      let tempAllProductVariantDetails = productVariantDetails?.nodes?.map(
        (product, index) => {
          index === 0 && setSelectedImage(product?.id);
          return {
            isSelected: index === 0,
            productId: product?.id,
            handle: product?.handle,
            data: product?.variants?.edges?.map((variant) => {
              return {
                id: variant?.node?.id,
                productId: product?.id,
                title: variant?.node?.title,
                selectedOption: variant?.node?.selectedOptions[0],
                qtyAvailable: variant?.node?.quantityAvailable,
                availableForSale: variant?.node?.availableForSale,
              };
            }),
          };
        },
      );

      setAllProductVariantDetails(tempAllProductVariantDetails);
      setDefaultSize(tempAllProductVariantDetails);
    }

    if (productVariantDetails?.length > 0) {
      setSelectedProductHandle(productVariantDetails[0]?.handle);
      setFetchProducts({
        result: productVariantDetails?.filter(
          (value) => value?.variants?.edges,
        ),
        isLoading: true,
        error: false,
      });
      const tempAllProductVariantDetails = productVariantDetails?.map(
        (product, index) => {
          index === 0 && setSelectedImage(product?.id);
          return {
            isSelected: index === 0,
            productId: product?.id,
            handle: product?.handle,
            data: product?.variants?.edges?.map((variant) => {
              return {
                id: variant?.node?.id,
                productId: product?.id,
                title: variant?.node?.title,
                selectedOption: variant?.node?.selectedOptions,
                qtyAvailable: variant?.node?.quantityAvailable,
                availableForSale: variant?.node?.availableForSale,
              };
            }),
          };
        },
      );
      setAllProductVariantDetails(tempAllProductVariantDetails);

      setDefaultSize(tempAllProductVariantDetails);
    }
  }, []);

  const setDefaultSize = (tempAllProductVariantDetails) => {
    // handle by default selection of free size
    const size = tempAllProductVariantDetails?.filter(
      (item) => item?.isSelected,
    )[0]?.data;
    if (size?.length == 1 && size[0].qtyAvailable > 0) {
      chooseSizeHandler(size[0], 0);
      setIsUpdateATB(false);
    }
  };

  const [isScrollable, setIsScrollable] = useState(false);

  useEffect(() => {
    Animated.timing(opacity, {
      toValue: 1, // Change to 1 to make it visible
      duration: 150,
      useNativeDriver: true,
      easing: Easing.bezier(0.5, 0.15, 0, 1),
    }).start();
  }, []);

  useEffect(() => {
    if (modalVisible) {
      bottomSheetRef.current.open();
    } else {
      bottomSheetRef.current.close();
    }
  }, [modalVisible]);

  const onVariantColorChange = (item) => {
    if (selectedImage == item?.id) return;
    selectedVariantImageHandler(item?.id);
    setIsAtbBtnLoading(true);
    setIsAddedATB(false);
    setIsUpdateATB(false);
    setSelectedProductHandle(item?.handle);
    const size = item?.variants?.edges;
    if (size?.length == 1 && size[0]?.node?.quantityAvailable > 0) {
      let updatedSize = {
        productId: item?.id,
        id: size[0]?.node?.id,
        qtyAvailable: size[0]?.node?.quantityAvailable,
        title: item?.title,
      };
      chooseSizeHandler(updatedSize, 0);
      setIsUpdateATB(false);
    }
  };

  useEffect(() => {
    setPageState({ quantity: quantity });
  }, [quantity])

  return (
    <View>
      <RBSheet
        ref={bottomSheetRef}
        openDuration={100}
        customStyles={{
          container: [styles.rbsheetContainer, { height: 430 + insets.bottom }],
          draggableIcon: {
            width: 50,
          },
        }}
        closeOnDragDown={true}
        animationType="slide"
        useNativeDriver
        onClose={toggleModal}>
        <TouchableWithoutFeedback onPress={toggleModal}>
          {/* <View style={styles.modalContainer}> */}
          {fetchProducts?.result?.length === 0 ? (
            <TouchableWithoutFeedback>
              <View
                style={[
                  styles.modalContent,
                  {
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: height * 0.5,
                  },
                ]}>
                {/* <ActivityIndicator /> */}
                <Text>Loading...</Text>
              </View>
            </TouchableWithoutFeedback>
          ) : (
            fetchProducts?.result?.length > 0 && (
              <TouchableWithoutFeedback>
                <View style={styles.modalContent}>
                  <>
                    {/* divider */}
                    {/* <View style={styles.modalHeader}>
                      <View style={styles.modalHeaderDividerContainer}>
                        <Text style={styles.modalHeaderDivider}></Text>
                      </View>
                    </View> */}
                    {/* close btn */}
                    <View style={styles.closeBtnContainer}>
                      <Pressable
                        disabled={!isAtbBtnLoading}
                        onPress={toggleModal}
                        style={styles.closeBtn}>
                        <Text style={styles.closeIcon}>
                          <Icon name="close" size={20} color="black" />
                        </Text>
                      </Pressable>
                    </View>
                    {/* colors-varient/Size */}
                    <View>
                      {/* Choose Image */}
                      <View style={{ paddingHorizontal: 10 }}>
                        <Text
                          style={{
                            fontSize: fonts._14,
                            color: '#121212',
                            textAlign: 'left',
                            fontFamily: fonts.FONT_FAMILY.SemiBold,
                            marginBottom: 8,
                            paddingHorizontal: widthPixel(10),
                            letterSpacing: 0.6,
                          }}>
                          {`Colours`}
                          <Text
                            style={{
                              fontSize: fonts._14,
                              color: '#121212',
                              fontFamily: fonts.FONT_FAMILY.Regular,
                              paddingHorizontal: widthPixel(10),
                              letterSpacing: 0.6,
                            }}>
                            {`  (${fetchProducts?.result?.length} Colour)`}
                          </Text>
                        </Text>
                      </View>
                      {/* Prodcut images */}
                      <View style={{ backgroundColor: 'white' }}>
                        <View>
                          <ScrollView
                            horizontal
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={{
                              paddingHorizontal: widthPixel(30),
                            }}>
                            <FlatList
                              data={fetchProducts?.result}
                              horizontal
                              showsHorizontalScrollIndicator={false}
                              contentContainerStyle={{ flexGrow: 1 }}
                              renderItem={({ item, index }) => {
                                return (
                                  <View key={index} style={styles.mainBox}>
                                    <Pressable
                                      disabled={!isAtbBtnLoading}
                                      onPress={() => {
                                        onVariantColorChange(item);
                                      }}
                                      style={{
                                        height: 100,
                                        width: 85,
                                        borderWidth: 2,
                                        borderRadius: 10,
                                        borderColor:
                                          selectedImage === item?.id
                                            ? '#221f20'
                                            : 'transparent',
                                      }}>
                                      {item?.images?.nodes && (
                                        <ShopifyImage
                                          source={{
                                            uri: item?.images?.nodes[0]?.src,
                                          }}
                                          style={{
                                            borderWidth: 0,
                                            height: '100%',
                                            width: '100%',
                                            borderRadius: 10,
                                          }}
                                          resizeMode={FastImage.resizeMode.contain}
                                          maxWidth={200}
                                          alt={item?.images?.nodes[0]?.altText}
                                        />
                                      )}

                                      {item?.images.edges && (
                                        <ShopifyImage
                                          source={{
                                            uri: item?.images?.edges[0]?.node
                                              ?.url, // Use the URL from edges
                                            priority: FastImage.priority.normal, // Optional: Set loading priority
                                          }}
                                          style={{
                                            borderWidth: 0,
                                            height: '100%',
                                            width: '100%',
                                            borderRadius: 10,
                                          }}
                                          resizeMode={
                                            FastImage.resizeMode.contain
                                          } // Adjust the resizeMode
                                          maxWidth={200}
                                        />
                                      )}
                                    </Pressable>
                                    <Text
                                      // numberOfLines={2}
                                      ellipsizeMode="tail"
                                      style={{
                                        fontSize: fontSizerMedium(width),
                                        color: '#221f20',
                                        fontFamily: 'Jost-Regular',
                                        marginTop: 8,
                                        paddingHorizontal: 4,
                                        width: 85,
                                      }}
                                      numberOfLines={2}>
                                      {(item?.metafields &&
                                        item?.metafields[1]?.value) ||
                                        item?.metafield?.value}
                                    </Text>
                                  </View>
                                );
                              }}
                            />
                          </ScrollView>
                        </View>
                      </View>

                      {/* Choose Size */}
                      <>
                        <View
                          style={{
                            flexDirection: 'row',
                            justifyContent: 'flex-start',
                            alignItems: 'center',
                            backgroundColor: 'white',
                            borderWidth: 0,
                            paddingHorizontal: widthPixel(10),
                          }}>
                          <Text
                            style={{
                              fontSize: fonts._14,
                              color: '#121212',
                              textAlign: 'left',
                              fontFamily: fonts.FONT_FAMILY.SemiBold,
                              marginTop: 10,
                              paddingHorizontal: widthPixel(10),
                            }}>
                            Choose Size
                          </Text>
                          {warningMessage?.isVisible && (
                            <Animated.Text
                              style={[
                                styles.WarningText,
                                {
                                  fontFamily: 'Jost-Regular',
                                },
                              ]}>
                              {warningMessage?.msg}
                            </Animated.Text>
                          )}
                        </View>

                        {/* ## Size list ## */}
                        <ScrollView
                          horizontal
                          showsHorizontalScrollIndicator={false}
                          scrollEnabled={isScrollable} // Dynamically enable or disable scrolling
                          onContentSizeChange={(contentWidth) => {
                            // Enable scrolling only if content width exceeds screen width
                            setIsScrollable(contentWidth > width + 30);
                          }}
                          contentContainerStyle={{
                            paddingVertical: 6,
                            paddingBottom: 8,
                            paddingHorizontal: widthPixel(30),
                          }}>
                          {allProductVariantDetails
                            ?.filter((item) => item?.isSelected)[0]
                            ?.data?.map((selectedItem, index) => {
                              const isSelected = selectedSize === index;
                              return (
                                <>
                                  <View
                                    key={index}
                                    style={{
                                      alignItems: 'center',
                                      margin: 5,
                                      marginTop: 2,
                                    }}>
                                    <Pressable
                                      disabled={
                                        !(selectedItem?.qtyAvailable > 0) ||
                                        !isAtbBtnLoading
                                      }
                                      onPress={() => {
                                        chooseSizeHandler(selectedItem, index);
                                        setIsUpdateATB(false);
                                      }}>
                                      {isSelected &&
                                        selectedItem?.qtyAvailable > 0 ? (
                                        <LinearGradient
                                          colors={['#000', '#444343']}
                                          start={{ x: 0, y: 0 }}
                                          end={{ x: 1, y: 1 }}
                                          style={[
                                            styles.sizeVariantButton,
                                            styles.selectedButton,
                                            {
                                              borderWidth: isSelected ? 0 : 1,
                                              minWidth:
                                                selectedItem?.title?.length > 2
                                                  ? 'auto'
                                                  : 67,
                                              paddingHorizontal:
                                                selectedItem?.title?.length > 2
                                                  ? 6
                                                  : 6,
                                            },
                                          ]}>
                                          <Text
                                            style={
                                              selectedItem?.qtyAvailable > 0
                                                ? [
                                                  styles.text,
                                                  styles.selectedText,
                                                ]
                                                : [
                                                  styles.textDisabled,
                                                  styles.selectedText,
                                                ]
                                            }>
                                            {selectedItem?.title}
                                          </Text>
                                        </LinearGradient>
                                      ) : (
                                        <View style={{ overflow: 'hidden', borderRadius: 10, }}>
                                          <View
                                            style={[
                                              styles.sizeVariantButton,
                                              {
                                                opacity:
                                                  selectedItem?.qtyAvailable > 0
                                                    ? 1
                                                    : 0.3,
                                                width:
                                                  selectedItem?.title?.length >
                                                    2
                                                    ? 62
                                                    : 62,
                                                paddingHorizontal:
                                                  selectedItem?.title?.length >
                                                    2
                                                    ? 6
                                                    : 6,
                                                overflow: 'hidden',
                                              },
                                            ]}>
                                            <Text
                                              style={[
                                                styles.text,
                                                {
                                                  fontFamily:
                                                    fonts.FONT_FAMILY.Medium,
                                                },
                                              ]}>
                                              {selectedItem?.title}
                                            </Text>
                                          </View>
                                          {!(
                                            selectedItem?.qtyAvailable > 0
                                          ) && (
                                              <View style={styles.afterContent} />
                                            )}
                                        </View>
                                      )}
                                    </Pressable>
                                  </View>
                                </>
                              );
                            })}
                        </ScrollView>
                      </>
                    </View>

                    {/* Bottom Container */}
                    <View style={styles.bottomContainerModal}>
                      {/* View Product eye icon */}
                      <Pressable
                        style={[styles.viewProductModal]}
                        disabled={!isAtbBtnLoading}
                        onPress={() => {
                          props.onAction({
                            action: 'OPEN_PRODUCT',
                            params: {
                              productHandle: `${selectedProductHandle}`,
                            },
                          });
                          toggleModal();
                        }}>
                        <Svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="22"
                          height="15"
                          viewBox="0 0 23 16"
                          fill="none">
                          <Path
                            d="M11.4951 12.4195C13.7976 12.4195 15.6698 10.4615 15.6698 8.05496C15.6698 5.64842 13.7976 3.69043 11.4951 3.69043C9.19254 3.69043 7.32031 5.64776 7.32031 8.05496C7.32031 10.4622 9.19254 12.4195 11.4951 12.4195ZM11.4951 5.22362C12.988 5.22362 14.2032 6.49387 14.2032 8.05487C14.2032 9.6157 12.988 10.8861 11.4951 10.8861C10.0021 10.8861 8.78692 9.6157 8.78692 8.05487C8.78692 6.49404 10.0021 5.22362 11.4951 5.22362Z"
                            fill="#221F20"></Path>
                          <Path
                            d="M11.4961 15.2046C17.4635 15.2046 22.1516 8.78941 22.3482 8.51561C22.5454 8.24267 22.5454 7.8662 22.3482 7.59341C22.1516 7.32047 17.4635 0.905273 11.4961 0.905273C5.52867 0.905273 0.840548 7.32049 0.644022 7.59341C0.446784 7.86636 0.446784 8.24282 0.644022 8.51561C0.840607 8.78941 5.52867 15.2046 11.4961 15.2046ZM11.4961 2.43859C15.8653 2.43859 19.6592 6.63661 20.8158 8.05475C19.6593 9.47373 15.8653 13.6709 11.4961 13.6709C7.12685 13.6709 3.33295 9.4736 2.17642 8.05475C3.33286 6.63577 7.12685 2.43859 11.4961 2.43859Z"
                            fill="#221F20"></Path>
                        </Svg>
                      </Pressable>

                      {/* quantity */}
                      {finalSelectedProduct?.availableQty && (
                        /* Minus */
                        <Pressable
                          activeOpacity={1}
                          style={{
                            width: '40%',
                            justifyContent: 'center',
                          }}>
                          <View style={styles.quantityContainer}>
                            <Pressable
                              disabled={
                                finalSelectedProduct?.availableQty === 1 ||
                                !isAtbBtnLoading ||
                                quantity === 1
                              }
                              activeOpacity={
                                finalSelectedProduct?.availableQty === 1
                                  ? 1
                                  : 0.2
                              }
                              onPress={() => {
                                if (quantity > 1) {
                                  if (isAddedATB) {
                                    setIsUpdateATB(true);
                                  } else {
                                    setIsUpdateATB(false);
                                  }
                                  updateQuantity(quantity - 1);
                                  setFinalSelectedProduct((prev) => {
                                    return {
                                      ...prev,
                                      qty: prev.qty - 1,
                                    };
                                  });
                                }
                              }}
                              style={styles.quantityButton}>
                              {isAddedATB && quantity === 1 ? (
                                <Pressable
                                  style={[styles.deleteButton]}
                                  onPress={deleteFromCart}>
                                  {loading ? (
                                    <ActivityIndicator
                                      size="small"
                                      color="#212121"
                                    />
                                  ) : (
                                    <Svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="15"
                                      height="17"
                                      viewBox="0 0 15 17"
                                      fill="none">
                                      <Path
                                        d="M4.61565 2.2772H4.46387C4.54735 2.2772 4.61565 2.2089 4.61565 2.12542V2.2772H10.3835V2.12542C10.3835 2.2089 10.4518 2.2772 10.5353 2.2772H10.3835V3.64328H11.7496V2.12542C11.7496 1.45566 11.2051 0.911133 10.5353 0.911133H4.46387C3.79411 0.911133 3.24958 1.45566 3.24958 2.12542V3.64328H4.61565V2.2772ZM14.1782 3.64328H0.82101C0.485184 3.64328 0.213867 3.91459 0.213867 4.25042V4.85756C0.213867 4.94104 0.282171 5.00935 0.365653 5.00935H1.51164L1.98027 14.9323C2.01063 15.5793 2.54568 16.0897 3.19266 16.0897H11.8065C12.4554 16.0897 12.9885 15.5812 13.0189 14.9323L13.4875 5.00935H14.6335C14.717 5.00935 14.7853 4.94104 14.7853 4.85756V4.25042C14.7853 3.91459 14.514 3.64328 14.1782 3.64328ZM11.6604 14.7236H3.33876L2.8796 5.00935H12.1196L11.6604 14.7236Z"
                                        fill="black"></Path>
                                    </Svg>
                                  )}
                                </Pressable>
                              ) : (
                                <Svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width={qtyIcon(width)}
                                  height={qtyIcon(width) + 4}
                                  viewBox="0 0 15 20"
                                  fill="none">
                                  <Path
                                    d="M1 10H13.5"
                                    stroke={
                                      quantity === 1 ? '#b2a9a9' : 'black'
                                    }
                                    strokeWidth={2}
                                    strokeLinecap="round"></Path>
                                </Svg>
                              )}
                            </Pressable>
                            {/* Qty */}
                            <Text
                              style={[
                                styles.quantityText,
                                {
                                  fontFamily: 'Jost-Medium',
                                  fontSize: qtyFontSize(width),
                                },
                              ]}>
                              {quantity}
                            </Text>
                            {/* Plus */}
                            <Pressable
                              disabled={
                                finalSelectedProduct?.availableQty === 1 ||
                                finalSelectedProduct?.availableQty <=
                                quantity ||
                                quantity === 10 ||
                                !isAtbBtnLoading
                              }
                              activeOpacity={
                                finalSelectedProduct?.availableQty === 1 ||
                                  finalSelectedProduct?.availableQty <= quantity
                                  ? 1
                                  : 0.8
                              }
                              onPress={() => {
                                if (quantity < 10) {
                                  if (isAddedATB) {
                                    setIsUpdateATB(true);
                                  } else {
                                    setIsUpdateATB(false);
                                  }
                                  updateQuantity(quantity + 1);
                                  setFinalSelectedProduct((prev) => {
                                    return {
                                      ...prev,
                                      qty: prev.qty + 1,
                                    };
                                  });
                                }
                              }}
                              style={styles.quantityButton}>
                              <Text
                                style={[
                                  styles.buttonText,
                                  {
                                    opacity:
                                      finalSelectedProduct?.availableQty ===
                                        1 ||
                                        finalSelectedProduct?.availableQty <=
                                        quantity
                                        ? 0.8
                                        : 1,
                                    marginRight: 10,
                                  },
                                ]}>
                                <Svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width={qtyIcon(width)}
                                  height={qtyIcon(width)}
                                  viewBox="0 0 16 16"
                                  fill="none">
                                  <Path
                                    d="M1 8H15"
                                    stroke={
                                      finalSelectedProduct?.availableQty ===
                                        1 ||
                                        quantity === 10 ||
                                        finalSelectedProduct?.availableQty <=
                                        quantity
                                        ? '#b2a9a9'
                                        : 'black'
                                    }
                                    strokeWidth={2}
                                    strokeLinecap="round"></Path>
                                  <Path
                                    d="M8 15V1"
                                    stroke={
                                      finalSelectedProduct?.availableQty ===
                                        1 ||
                                        quantity === 10 ||
                                        finalSelectedProduct?.availableQty <=
                                        quantity
                                        ? '#b2a9a9'
                                        : 'black'
                                    }
                                    strokeWidth={2}
                                    strokeLinecap="round"></Path>
                                </Svg>
                              </Text>
                            </Pressable>
                          </View>
                        </Pressable>
                      )}

                      {/* Add to Bag */}
                      <Pressable
                        disabled={
                          !isAtbBtnLoading || (isAddedATB && !isUpdateATB)
                        }
                        style={[
                          styles.addToBagBtnModal,
                          {
                            width: finalSelectedProduct?.availableQty
                              ? '40%'
                              : '75%',
                          },
                        ]}
                        // onPress={!isAtbBtnLoading && addToBagHandler}
                        onPress={startAnimation}
                        activeOpacity={0.9}>
                        <LinearGradient
                          colors={['#221f20', '#505050']}
                          start={{ x: 1.0, y: 0.5 }}
                          end={{ x: 0.0, y: 0.5 }}
                          style={[
                            styles.bagButton,
                            styles.selectedButton,
                            {
                              width: '100%',
                              borderWidth: 0,
                              // height: '100%',
                            },
                          ]}>
                          <View
                            style={{
                              overflow: 'hidden',
                              width: '100%',
                              height: '100%',
                              justifyContent: 'center',
                              alignItems: 'center',
                              // backgroundColor: 'cyan'
                            }}>
                            {isAddedATB && quantity === cartQuantity
                              ? isAtbBtnLoading && (
                                <Text
                                  disabled={true}
                                  style={[styles.addToBagText]}>
                                  {'ADDED'}
                                </Text>
                              )
                              : isAddedATB && isUpdateATB
                                ? isAtbBtnLoading && (
                                  <Text style={[styles.addToBagText]}>
                                    {'UPDATE BAG'}
                                  </Text>
                                )
                                : isAtbBtnLoading && (
                                  <Text style={[styles.addToBagText]}>
                                    {'ADD TO BAG'}
                                  </Text>
                                )}

                            {!isAtbBtnLoading && (
                              <>
                                <Animated.View
                                  style={{
                                    transform: [
                                      { translateX: addedTextTranslateX },
                                    ],
                                    top: 12,
                                    left: -400,
                                  }}>
                                  <Text
                                    style={[
                                      styles.addToBagText,
                                      { zIndex: 10 },
                                    ]}>
                                    ADDED
                                  </Text>
                                </Animated.View>

                                <Animated.View
                                  style={{
                                    transform: [{ translateX }],
                                    bottom: 10,
                                    left: -200,
                                  }}>
                                  <Text>
                                    <Svg
                                      id="atc-icon-bag"
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="16"
                                      height="20"
                                      viewBox="0 0 16 20"
                                      fill="white">
                                      <Path d="M7.6922 1C9.75925 1 11.4505 2.66901 11.4505 4.70462V6.2312L14.3736 6.23106C14.7286 6.23106 15 6.49562 15 6.8417V16.4084C15 18.0978 13.6011 19.4615 11.8681 19.4615H3.51651C1.78347 19.4615 0.38461 18.0978 0.38461 16.4084V6.8417C0.38461 6.49562 0.655993 6.23106 1.01099 6.23106H3.934V4.70448C3.934 2.66909 5.62515 1 7.6922 1ZM10.1977 4.70427C10.1977 3.34057 9.07023 2.22099 7.6922 2.22099C6.31416 2.22099 5.18668 3.34043 5.18668 4.70427V6.23085H10.1977V4.70427ZM1.63708 7.47256V16.4286C1.63708 17.4464 2.47221 18.2605 3.51622 18.2605L11.8682 18.2604C12.9122 18.2604 13.7473 17.4462 13.7473 16.4285V7.47243H11.4505V8.53081C11.4505 8.87688 11.1792 9.14144 10.8242 9.14144C10.4692 9.14144 10.1978 8.87688 10.1978 8.53081V7.47243H5.18675V8.53081C5.18675 8.87688 4.91537 9.14144 4.56038 9.14144C4.20538 9.14144 3.934 8.87688 3.934 8.53081V7.47243L1.63708 7.47256Z"></Path>
                                    </Svg>
                                  </Text>
                                </Animated.View>

                                <Animated.View
                                  style={{
                                    transform: [
                                      { translateY: dropY },
                                      { translateX: moveRight },
                                    ],
                                    opacity,
                                    top: -8,
                                    left: -0.4,
                                  }}>
                                  <Text>
                                    <Svg
                                      id="atc-icon-rectangle"
                                      width="5"
                                      height="5"
                                      viewBox="0 0 5 5"
                                      fill="none"
                                      xmlns="http://www.w3.org/2000/svg">
                                      <Rect
                                        width="5"
                                        height="5"
                                        rx="1"
                                        fill="white"></Rect>
                                    </Svg>
                                  </Text>
                                </Animated.View>
                              </>
                            )}
                          </View>
                        </LinearGradient>
                      </Pressable>
                    </View>
                  </>
                </View>
              </TouchableWithoutFeedback>
            )
          )}
          {/* </View> */}
        </TouchableWithoutFeedback>
      </RBSheet>
    </View>
  );
}

const styles = StyleSheet.create({
  rbsheetContainer: {
    backgroundColor: 'rgb(254,254,255)',
    width: '100%',
    height: 430,
    borderTopLeftRadius: widthPixel(30),
    borderTopRightRadius: widthPixel(30),
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlay: {
    // Covers the entire screen
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black background
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  modalContent: {
    backgroundColor: 'rgb(254,254,255)',
    // justifyContent: 'space-between',
    width: '100%',
    height: '100%', // Half of the screen - default *478 change to this after work done
    borderTopLeftRadius: widthPixel(30),
    borderTopRightRadius: widthPixel(30),
  },
  modalHeader: {
    paddingHorizontal: 20,
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modalHeaderDividerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalHeaderDivider: {
    width: width * 0.14,
    height: height * 0.007,
    borderRadius: 20,
    backgroundColor: '#D0D4CA',
  },
  closeBtnContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 20,
  },
  closeBtn: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
    elevation: 2,
    shadowOpacity: 0.1,
    shadowRadius: 3,
    shadowOffset: { width: 0, height: 5 },
    borderRadius: 18,
  },
  // modal varient
  mainBox: {
    alignItems: 'flex-start',
    // paddingRight: 10,
    paddingVertical: 8,
    overflow: 'hidden',
    marginRight: 5,
  },

  // modal size
  sizeVarientButton: {
    height: height * 0.075,
    width: width * 0.154,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, .2)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // modal quantity
  quantityContainer: {
    flexDirection: 'row',
    width: '96%',
    height: 56,
    justifyContent: 'space-around',
    alignItems: 'center',
    alignSelf: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    borderTopEndRadius: 4,
    borderBottomEndRadius: 4,
    marginLeft: 20,
  },
  quantityButton: {
    paddingHorizontal: 15,
    paddingVertical: 15,
    // backgroundColor: 'yellow',
  },
  buttonText: {
    fontSize: 18,
    width: width * 0.125,
    height: height * 0.062,
  },
  quantityText: {
    color: '#221f20',
  },

  // modal button
  buttoncontainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: 'white',
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: 'black',
    borderWidth: 1,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 5,
    margin: 10,
  },
  buttonBag: {
    alignItems: 'center',
    backgroundColor: 'grey',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    margin: 10,
  },
  buttonText: {
    marginLeft: 5,
    fontSize: 16,
    fontWeight: '400',
  },
  deleteButton: {
    padding: widthPixel(8),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonTextBag: {
    fontSize: 16,
    color: 'white',
    fontWeight: '500',
  },
  sizeVariantContainer: {
    alignItems: 'center',
    alignContent: 'center',
    margin: 5,
  },
  sizeVariantButtonWrapper: {
    borderRadius: 5,
  },
  sizeVariantButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 60,
    width: 67,
    minWidth: 67,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, .2)',
    // backgroundColor: 'red',
  },
  bagButton: {
    width: 56,
    width: widthPixel(56),
    borderRadius: 12,
    borderWidth: 0.66,
    borderColor: '#cecfce',
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedButton: {
    backgroundColor: 'transparent',
  },
  text: {
    fontSize: fonts._12,
    color: 'rgb(34, 31, 32)',
    textAlign: 'center',
  },
  textDisabled: {
    fontSize: fonts._16,
    color: '#0003',
  },
  selectedText: {
    color: 'white',
    fontFamily: fonts.FONT_FAMILY.Medium,
  },
  // Bottom Container
  bottomContainerModal: {
    height: 70,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingTop: 8,
    backgroundColor: 'white',
  },
  viewProductModal: {
    width: '20%',
    height: 56,
    borderWidth: 0.5,
    borderColor: 'rgb(34, 31, 32)',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    // marginBottom: 12,
  },
  viewProductIconModal: {
    position: 'absolute',
    top: '25%',
    left: '25%',
    textAlign: 'center',
  },
  addToBagBtnModal: {
    width: '45%',
    height: 56,
    // marginBottom: 12,
  },
  addToBagText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Jost-Regular',
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  WarningText: {
    marginTop: 15,
    fontSize: 15,
    letterSpacing: 0,
    color: 'red',
    lineHeight: heightPixel(20),
  },
  afterContent: {
    position: 'absolute',
    backgroundColor: '#ccc',
    width: 84,
    height: 2.4,
    borderRadius: 50,
    transform: [{ rotate: '-40deg' }],
    left: 2,
    top: '51%',
    marginLeft: -10,
    marginTop: -1,
  },
});
