import { appStorageApi } from '@appmaker-xyz/core';
import { isCartApiEnabled } from '@appmaker-xyz/shopify/helper/helper';

/**
 * Gets the checkout URL from the Shopify cart
 * 
 * This function retrieves the checkout URL from either the new Shopify Cart API
 * or the legacy checkout, depending on which is enabled in the application.
 * 
 * @returns {string|null} The checkout URL or null if not available
 */
export const getCheckoutUrl = () => {
  // Get the current checkout state based on which API is being used
  const isUsingCartApi = isCartApiEnabled();
  const appStorage = appStorageApi().getState();
  
  if (isUsingCartApi) {
    // If using the new Shopify Cart API
    const shopifyCart = appStorage.shopifyCart;
    return shopifyCart?.checkoutUrl;
  } else {
    // If using the legacy checkout
    const checkout = appStorage.checkout;
    return checkout?.webUrl;
  }
};

/**
 * Gets the full checkout data including URL and other details
 * 
 * @returns {Object} An object containing checkout information
 */
export const getCheckoutData = () => {
  const isUsingCartApi = isCartApiEnabled();
  const appStorage = appStorageApi().getState();
  const user = appStorage.user;
  
  if (isUsingCartApi) {
    const shopifyCart = appStorage.shopifyCart;
    return {
      checkoutUrl: shopifyCart?.checkoutUrl,
      cart: shopifyCart,
      user: user ? {
        accessToken: user.accessToken ? 'Present (not shown for security)' : 'Not present',
        email: user.email,
      } : null
    };
  } else {
    const checkout = appStorage.checkout;
    return {
      checkoutUrl: checkout?.webUrl,
      cart: checkout,
      user: user ? {
        accessToken: user.accessToken ? 'Present (not shown for security)' : 'Not present',
        email: user.email,
      } : null
    };
  }
};
