import { StyleSheet, Text, View } from 'react-native'
import React from 'react'
import { fonts, heightPixel, widthPixel } from '../../styles';
import Ripple from 'react-native-material-ripple';
import { SvgUri } from 'react-native-svg';
import { useCurrentUser } from '@appmaker-xyz/shopify';
import { getAutoLoginMyProfileRes, getIdFromGID } from '../../utils/Helper';


const AccountMenu = (props) => {
    const { attributes, innerBlocks, onAction } = props;

    const userRequestMetafields = [
        {
            namespace: "jockey_balance",
            key: "customer_balance",
        }];
    const { data } = useCurrentUser({ metafields: userRequestMetafields });
    const customerId = getIdFromGID(data?.customer?.id)




    function hasShowOnlyOnLoginYes(arr) {
        return arr.every(item => item?.attributes?.showOnlyOnLogin === "yes");
    }

    const isShowOnlyOnLogin = hasShowOnlyOnLoginYes(innerBlocks);

    const handleNavigation = async (attributes) => {
        const selectPageType = attributes?.selectPageType
        if (selectPageType == "webpage") {
            if (attributes?.showOnlyOnLogin == "yes") {
                try {

                    const multipassResponse = await getAutoLoginMyProfileRes(customerId, attributes?.urlOrName)

                    if (multipassResponse.status != 200) {
                        return;
                    }
                    onAction({
                        action: 'OPEN_WEBVIEW',
                        params: {
                            url: multipassResponse?.data?.response?.loginUrl,
                            title: attributes.mainHeading,
                            replacePage: false,
                        },
                    })

                    return JSON.stringify(data);
                } catch (err) {
                    console.error(err);
                    return JSON.stringify({ error: err.message });
                }
            }
            else {
                onAction({
                    action: 'OPEN_WEBVIEW',
                    params: {
                        url: attributes?.urlOrName,
                        title: attributes.mainHeading,
                        replacePage: false,
                    },
                })
            }
        }
        else {
            onAction({
                action: "OPEN_INAPP_PAGE",
                params: {
                    pageId: attributes?.urlOrName,
                    replacePage: false,
                },
            })
        }

    }

    if (isShowOnlyOnLogin & !data?.customer) {
        return <></>
    }
    return (           
        <View style={styles.listContainer}>
            <View style={styles.listHeader}>
                <Text style={styles.listHeaderText}>{attributes?.mainHeading}</Text>
            </View>

            {innerBlocks.map((item, index) => {
                const attributes = item?.attributes
                if (attributes?.showOnlyOnLogin == "yes" && !data?.customer) {
                    return <></>
                }
                return (
                    <>
                        <Ripple style={styles.listRipple} onPress={() => {
                            handleNavigation(attributes)
                        }
                        }
                        >
                            <View style={styles.listItemContainer}>
                                {/* <Image source={{ uri: attributes?.image?.url }} style={styles.image} /> */}
                                <SvgUri
                                    width={widthPixel(16)}
                                    height={widthPixel(16)}
                                    style={{ paddingTop: heightPixel(35) }}
                                    uri={attributes?.image?.url}
                                />
                                <View>
                                    <Text style={styles.listItemTitle}>{attributes.mainHeading}</Text>
                                    <Text style={styles.listItemSubtitle}>{attributes.subHeading}</Text>
                                </View>
                            </View>
                        </Ripple>

                        {index < innerBlocks.length - 1 && innerBlocks.length !== 1 && (
                            <View style={styles.listSeparator} />
                        )}
                    </>
                );
            })}
        </View>
    )
}

export default AccountMenu

const styles = StyleSheet.create({
    listContainer: {
        marginTop: widthPixel(10),
    },
    listHeader: {
        height: heightPixel(40),
        justifyContent: 'center',
        paddingLeft: widthPixel(18),
    },
    listHeaderText: {
        fontSize: fonts._18,
        fontFamily: fonts.FONT_FAMILY.Medium,
        color: 'black',
        opacity: 0.6,
    },
    listRipple: {
        // height: heightPixel(71),
        paddingVertical: widthPixel(15),
        paddingHorizontal: widthPixel(24),
    },
    listItemContainer: {
        flexDirection: 'row',
        gap: widthPixel(10),
        alignItems: 'flex-start',
    },
    listItemTitle: {
        color: 'black',
        fontSize: fonts._16,
        fontFamily: fonts.FONT_FAMILY.Medium,
        // lineHeight: heightPixel(18),
    },
    listItemSubtitle: {
        color: 'black',
        opacity: 0.6,
        fontSize: fonts._14,
        fontFamily: fonts.FONT_FAMILY.Regular,
    },
    listSeparator: {
        borderTopWidth: 0.5,
        borderColor: '#808080',
        opacity: 0.5,
    },
    image: {
        height: widthPixel(20),
        width: widthPixel(20),
    }
})