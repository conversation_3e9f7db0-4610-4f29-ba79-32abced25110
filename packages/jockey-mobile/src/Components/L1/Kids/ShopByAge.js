import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { ImageBackground } from 'react-native';
import { Pressable } from 'react-native';
import { Dimensions } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { fonts, heightPixel, widthPixel } from '../../../styles';

const ShopByAge = (props) => {
  const { attributes, innerBlocks, onAction } = props;

  const contentContainerStyle = attributes?.containerStyles;
  const backgroundImage = attributes?.backgroundimage?.url;

  console.log('backgroundImage', backgroundImage);

  return (
    <View style={contentContainerStyle}>
      <ImageBackground
        source={{ uri: backgroundImage }}
        style={styles.backgroundImage}>
        <View style={styles.overlayContainer}>
          {innerBlocks.map((item, index) => {
            const arrayString = item?.attributes?.gradientarray;
            const cleanedArrayString = arrayString
              .replace(/,\s+/g, ',')
              .replace(/'/g, '"');
            const array = JSON.parse(cleanedArrayString);

            console.log('array', array);

            return (
              <View
                key={index}
                style={{
                  position: 'relative',
                  top: `${item?.attributes?.topdistance}%`,
                  left: `${item?.attributes?.leftdistance}%`,
                }}>
                <Pressable
                  onPress={() => onAction(item?.attributes?.appmakerAction)}>
                  <View style={styles.container}>
                    <View style={styles.outerCircle}>
                      <LinearGradient
                        colors={array}
                        start={{ x: 2, y: 1 }} // 276 degrees (adjusted to approximate)
                        end={{ x: 0, y: 1 }}
                        style={styles.innerCircle}>
                        <Text
                          style={[
                            styles.topText,
                            item?.attributes?.yeartextStyles,
                          ]}>
                          {item?.attributes?.yearRange}
                        </Text>
                        <Text style={styles.bottomText}>Years</Text>
                      </LinearGradient>
                    </View>
                  </View>
                </Pressable>
              </View>
            );
          })}
        </View>
      </ImageBackground>
    </View>
  );
};

const { width, height } = Dimensions.get('window');
// const circleSize = width * 0.26;
const circleSize = wp('26%');

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    // width: width * 1,
    // height: height * 0.80,
    width: wp('100%'),
    height: hp('80%'),
  },
  outerCircle: {
    width: circleSize,
    height: circleSize,
    borderRadius: circleSize / 2,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: widthPixel(1),
    borderStyle: 'dotted',
    borderColor: '#FAF0F0',
  },
  innerCircle: {
    width: circleSize * 0.9,
    height: circleSize * 0.9,
    borderRadius: (circleSize * 0.9) / 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  topText: {
    flexWrap: 'wrap',
  },
  bottomText: {
    marginTop: heightPixel(10),
    color: '#221f20B3',
    fontSize: fonts._17,
    fontFamily: fonts.FONT_FAMILY.Regular,
    flexWrap: 'wrap',
  },
});

export default ShopByAge;
