import React from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { heightPixel, widthPixel } from '../../../styles';

const ComfortForActiveBoys = (props) => {
  const { attributes, innerBlocks, onAction } = props;

  const contentContainerStyle = attributes?.containerStyles;
  const mainHeadingTextStyles = attributes?.mainHeadingStyles;
  const subHeadingTextStyles = attributes?.subHeadingStyles;

  const renderItem = ({ item }) => (
    <View style={styles.cardContainer}>
      <Pressable onPress={() => onAction(item?.attributes?.appmakerAction)}>
        <View style={styles.gradientContainer}>
          <LinearGradient
            style={styles.gradientLayer}
            colors={['#0000006e', 'transparent']}
            start={{ x: 0.5, y: 1 }}
            end={{ x: 0.5, y: 0 }}
            locations={[0, 0.9, 0.9, 0.9, 0.9, 1]}
          />
        </View>
        <View style={styles.card}>
          <Image
            source={{ uri: item?.attributes?.blockimage?.url }}
            style={styles.image}
          />
        </View>
      </Pressable>
    </View>
  );

  return (
    <View style={contentContainerStyle}>
      <View style={styles.comfortForBoysWrapper}>
        <View
          style={{
            flexDirection: 'row',
            marginVertical: 10,
          }}>
          <Text style={[mainHeadingTextStyles, { marginRight: 5 }]}>
            {attributes?.heading}
          </Text>
          <Text style={subHeadingTextStyles}>{attributes?.subheading}</Text>
        </View>
        <FlatList
          data={innerBlocks}
          renderItem={renderItem}
          keyExtractor={(item, index) => index.toString()}
          numColumns={3}
          contentContainerStyle={styles.comfortForBoysContainer}
        />
      </View>
    </View>
  );
};

const { width, height } = Dimensions.get('window');
const cardWidth = width * 0.32;
const cardMargin = width * 0.006;

const styles = StyleSheet.create({
  comfortForBoysWrapper: {
    padding: 3,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },

  comfortForBoysContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPixel(15),
  },

  cardContainer: {
    width: cardWidth,
    borderRadius: widthPixel(18),
    marginVertical: heightPixel(10),
    marginHorizontal: cardMargin,
    backgroundColor: '#fff',
  },
  card: {
    width: cardWidth,
    height: height * 0.3,
    // aspectRatio:50/104,
    borderRadius: widthPixel(18),
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  gradientLayer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 2,
  },
  gradientContainer: {
    width: cardWidth,
    height: height * 0.3,
    overflow: 'hidden',
    position: 'absolute',
    zIndex: 3,
    borderRadius: widthPixel(31),
  },
});

export default ComfortForActiveBoys;
