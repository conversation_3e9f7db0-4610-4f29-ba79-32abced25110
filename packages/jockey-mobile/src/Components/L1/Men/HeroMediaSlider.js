import React, { useEffect, useRef, useState } from 'react';
import {
  ScrollView,
  View,
  Dimensions,
  ActivityIndicator,
  Pressable,
  StyleSheet,
  Image,
} from 'react-native';
import Video from 'react-native-video';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { heightPixel, widthPixel } from '../../../styles';
import { CarouselPagination } from '../../common/CarouselPagination';
const { width: screenWidth } = Dimensions.get('window');

const MediaSwiper = (props) => {
  const { attributes, innerBlocks, onAction } = props;
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [muted, setMuted] = useState(true);
  const scrollViewRef = useRef(null);

  // useEffect(() => {
  //   setIsLoading(true);
  // }, []);

  useEffect(() => {
    setMuted(true);
  }, [currentIndex]);

  const handleScroll = (event) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(offsetX / screenWidth);
    setCurrentIndex(newIndex);
  };


  const handleDotPress = (index) => {
      setCurrentIndex(index);
  };


  const handleVideoEnd = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % innerBlocks.length);
  };

  const renderPaginationDots = () => (
    <View style={styles.pagination}>
      {innerBlocks?.map((_, index) =>
        <CarouselPagination
          isActive={index === currentIndex}
          index={index}
          onDotPress={handleDotPress}
          activeColor="#000"
          inActiveColor='#221f205e'
        />
      )}
    </View>
  );

  const renderItem = (item, index) => {
    const mediatype = attributes?.mediatype;
    if (mediatype === 'video') {
      return (
        <View key={index} style={styles.itemContainer}>
          {isLoading && (
            <ActivityIndicator
              size="large"
              color="black"
              style={styles.loadingIndicator}
            />
          )}
          <Video
            key={
              currentIndex === index
                ? `video-${index}`
                : `paused-video-${index}`
            }
            source={{
              uri: item?.attributes?.media?.url,
              // uri: "https://www.jockey.in/cdn/shop/videos/c/vp/229481628077426b9e5c0fde818e3e16/229481628077426b9e5c0fde818e3e16.SD-480p-1.5Mbps-34170277.mp4?v=0"
            }}
            style={styles.video}
            muted={muted}
            repeat={false}
            resizeMode="stretch"
            paused={currentIndex !== index}
            controls={false}
            onLoadStart={() => setIsLoading(true)}
            onBuffer={({ isBuffering }) => setIsLoading(isBuffering)}
            onLoad={() => setIsLoading(false)}
            onEnd={handleVideoEnd}
          />
          <Pressable onPress={() => setMuted(!muted)} style={styles.muteButton}>
            <Icon
              name={muted ? 'volume-off' : 'volume-up'}
              size={30}
              color="white"
            />
          </Pressable>
        </View>
      );
    } else {
      return (
        <View key={index} style={styles.slide}>
          <Pressable onPress={() => onAction(item?.attributes?.appmakerAction)}>
            <Image
              source={{ uri: item?.attributes?.image?.url }}
              style={styles.imagemedia}
              resizeMode="contain"
            />
          </Pressable>
        </View>
      );
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        style={styles.scrollView}
        contentOffset={{ x: currentIndex * screenWidth, y: 0 }}
        ref={scrollViewRef}
        >
        {innerBlocks.map((item, index) => renderItem(item, index))}
      </ScrollView>
      {innerBlocks.length > 1 && renderPaginationDots()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    width: '100%',
  },
  itemContainer: {
    width: screenWidth,
    // alignItems: 'center',
    // justifyContent: 'center',
    position: 'relative', // Ensure relative positioning for absolute children
    borderRadius: widthPixel(15),
    padding: widthPixel(8),
    // overflow: 'hidden',
  },
  video: {
    borderRadius: widthPixel(15),
    height: heightPixel(375),
  },
  loadingIndicator: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }],
  },
  muteButton: {
    position: 'absolute',
    bottom: heightPixel(15),
    right: widthPixel(20),
    backgroundColor: 'transparent',
    borderRadius: widthPixel(50),
    padding: widthPixel(10),
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPixel(24),
  },
});

export default MediaSwiper;
