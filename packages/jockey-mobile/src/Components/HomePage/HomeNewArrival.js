import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Text,
  Dimensions,
  ScrollView,
  Animated,
  Pressable,
} from 'react-native';
import Swiper from 'react-native-swiper';
import LinearGradient from 'react-native-linear-gradient';
import Svg, { Path } from 'react-native-svg';
import { useDataSourceV2 } from '@appmaker-xyz/core';
import HomeFilterButton from '../common/HomeFilterButton';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { fonts, getVerticalPadding, heightPixel, widthPixel } from '../../styles';
import FastImage from 'react-native-fast-image';
import ShopifyImage from '../ShopifyImage';

const { width, height } = Dimensions.get('window');

export default function NewArrival1(props) {
  const { attributes, onAction, innerBlocks } = props;
  const setHeadingDefault =
    attributes?.selectDefaultButtom === 'tag1'
      ? attributes?.tag1?.toLowerCase()
      : attributes?.tag2?.toLowerCase();

  const scrollViewRef = useRef(null);
  const [activeCategory, setActiveCategory] = useState(setHeadingDefault);
  const [opacity, setOpacity] = useState(new Animated.Value(1));

  const arrayOfGids = useMemo(
    () => innerBlocks
      .map((block) => block?.attributes?.product)
      .filter((product) => product !== undefined), // Filter out undefined
    [innerBlocks]
  );
  const [{ isFetching }, { item }] = useDataSourceV2({
    dataSource: {
      source: 'shopify',
      methodName: 'gqlQuery',
      dataExtractor: (response) => response?.data,
      params: {
        query: `
        {
          nodes(ids: ${JSON.stringify(arrayOfGids)}) {
            ... on Product {
              id
              metafield(key: "style_number", namespace: "custom") {
                key
                value
              }
              title
              handle
            }
          }
        }`,
      },
    },
  });

  const [combinedData, setCombinedData] = useState([]);

  useEffect(() => {
    if (item?.data?.nodes) {
      const mergedData = innerBlocks.map((block) => {
        const matchingProduct = item?.data?.nodes?.find(
          (product) => product?.id === block?.attributes?.product,
        );

        return {
          ...block,
          productDetails: matchingProduct || {},
        };
      });

      setCombinedData(mergedData);
    }
  }, [item, innerBlocks]);

  const data1 = attributes?.tag1?.toLowerCase();
  const data2 = attributes?.tag2?.toLowerCase();

  const data = {
    [data1]: combinedData.filter(
      (block) => block.attributes.selectCategory === 'tag1',
    ),
    [data2]: combinedData.filter(
      (block) => block.attributes.selectCategory === 'tag2',
    ),
  };

  if (isFetching) {
    return (
      <View>
        <Text>Loading...</Text>
      </View>
    );
  }

  const handleCategoryButtonClick = (category) => {
    if (activeCategory !== category) {
      Animated.timing(opacity, {
        toValue: 0,
        duration: 100,
        useNativeDriver: false,
      }).start(() => {
        setActiveCategory(category);
        setOpacity(new Animated.Value(1));

        if (scrollViewRef.current) {
          scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: false });
        }
      });
    }
  };

  const mainHeadingTextStyles = {
    ...attributes?.mainHeadingTextStyles,
  };

  const subHeadingTextStyles = {
    ...attributes?.subHeadingTextStyles,
  };

  const contentContainerStyle = {
    ...attributes?.containerStyles,
  };

  return (
    <LinearGradient
      colors={[
        'rgba(36, 43, 60, 1)',
        'rgba(122, 126, 137, 1)',
        'rgba(22, 28, 42, 1)',
      ]}
      start={{ x: 0.2, y: 0 }}
      end={{ x: 0.1, y: 1 }}
      locations={[0, 0.4948, 1]}
      style={{ height: 744, paddingBottom: 24 }}>
      <View style={[styles.newArrivalContainer, contentContainerStyle]}>
        <View style={styles.header}>
          <Text style={[mainHeadingTextStyles, { letterSpacing: 2.56 }]}>
            {attributes?.mainHeading}
          </Text>
          <Text style={subHeadingTextStyles}>{attributes?.subHeading}</Text>
        </View>

        <HomeFilterButton
          activeCategory={activeCategory}
          setActiveCategory={setActiveCategory}
          handleCategoryButtonClick={handleCategoryButtonClick}
          attributes={attributes}
          borderColor={'rgba(255,255,255,.69)'}
          boxbackgroundColor={'#FFFFFF'}
          buttonTextColor={'#ffffff'}
          buttonBackgroundColor={'transparent'}
          activeButtonTextColor={'#000000'}
          activeButtonBackgroundColor={'#FFFFFF'}
          fontFamily={'Jost-Regular'}
        />

        {getVerticalPadding(16)}

        <Swiper
          ref={scrollViewRef}
          containerStyle={[styles.sliderContainer, { zIndex: 2 }]}
          showsButtons={false}
          showsPagination={false}>
          {data[activeCategory].length > 0 ? (
            data[activeCategory].map((product, index) => {
              const handleName = product?.productDetails?.handle;

              const imageUrl = product?.attributes?.productimage?.url;
              const title = product?.productDetails?.title;
              const styleNumber = product?.productDetails?.metafield?.value;

              const truncateTitle = (title) => {
                const words = title?.split(' ');
                if (words?.length > 9) {
                  return words?.slice(0, 8).join(' ') + '...';
                }
                return title;
              };
              const truncatedTitle = truncateTitle(title);

              return (
                <View key={index} style={styles.slide}>
                  <Pressable
                    onPress={() => {
                      product?.attributes?.appmakerAction
                        ? onAction(product?.attributes?.appmakerAction)
                        : props.onAction({
                          action: 'OPEN_PRODUCT',

                          params: {
                            productHandle: `${product?.productDetails?.handle}`,
                            replacePage: false,
                          },
                        });
                    }}>
                    <View style={styles.exp}>
                      {imageUrl ? (
                        <View style={
                          {
                            height: 470,
                            width: 320,
                          }}>

                          <ShopifyImage
                            source={{
                              uri: imageUrl,
                            }}
                            style={styles.image}
                            resizeMode={FastImage.resizeMode.contain}
                            maxWidth={600}
                          />

                          <View style={styles.infoContainer}>
                            <View style={styles.titleContainer}>
                              <Text
                                style={{
                                  ...styles.title,
                                  //  fontSize: width * 0.04,
                                }}
                                numberOfLines={2}
                                ellipsizeMode="tail">
                                {title}
                              </Text>
                            </View>
                            {getVerticalPadding(8)}
                            <View style={styles.bottomRow}>
                              <View style={styles.styleNumberContainer}>
                                <Text
                                  style={{
                                    ...styles.styleNumber,
                                    // fontSize: width * 0.035,
                                  }}>
                                  {styleNumber || ''}
                                </Text>
                              </View>
                              <TouchableOpacity
                                style={styles.exploreButton}
                                onPress={() => {
                                  product?.attributes?.appmakerAction
                                    ? onAction(product?.attributes?.appmakerAction)
                                    : props.onAction({
                                      action: 'OPEN_PRODUCT',

                                      params: {
                                        productHandle: `${product?.productDetails?.handle}`,
                                        replacePage: false,
                                      },
                                    });
                                }}>
                                <Text
                                  style={{
                                    ...styles.exploreButtonText,
                                  }}>
                                  Explore
                                </Text>
                              </TouchableOpacity>
                            </View>
                          </View>

                        </View>

                      ) : (
                        <Text>No Image Available</Text>
                      )}

                    </View>
                  </Pressable>

                  <View
                    style={{
                      height: 330,
                      width: 305,
                      borderWidth: 0,
                      position: 'absolute',
                      zIndex: -1,
                      bottom: 45,
                      // top: height * 0.35,
                      borderRadius: 20,
                      overflow: 'hidden',
                    }}>
                    <LinearGradient
                      colors={[
                        '#38425a',
                        '#38425a',
                        '#293144',
                        '#293144',
                        '#38425a',
                        '#38425a',
                      ]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      locations={[0, 0.9, 0.9, 0.9, 0.9, 1]}
                      style={styles.gradientLayer}
                    />
                  </View>
                </View>
              );
            })
          ) : (
            <Text>No Products Available</Text>
          )}
        </Swiper>
        <View
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <Svg
            width="32"
            height="31"
            viewBox="0 0 32 31"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <Path
              id="Vector"
              d="M28.5811 14.3199C27.6812 14.3199 26.8731 14.7163 26.3197 15.3436C25.7666 14.7163 24.9585 14.3199 24.0586 14.3199C23.4374 14.3199 22.8617 14.5084 22.3822 14.831C21.9692 13.6567 20.8491 12.8123 19.5362 12.8123C18.9876 12.8123 18.4721 12.96 18.0289 13.2179L18.0286 4.52091C18.0286 2.85816 16.6763 1.50586 15.0136 1.50586C13.3508 1.50586 11.9985 2.85816 11.9985 4.52091V17.3272L8.10017 14.3787C6.96054 13.239 4.97368 13.2375 3.83694 14.3787C2.66103 15.553 2.66103 17.4675 3.82195 18.6269L14.4666 29.9102C14.6083 30.0621 14.8057 30.1481 15.0136 30.1481H27.8273C28.1725 30.1481 28.474 29.9128 28.5584 29.5768L31.5734 17.5169C31.5887 17.458 31.5963 17.3963 31.5963 17.3345C31.5963 15.6717 30.244 14.3194 28.5812 14.3194L28.5811 14.3199ZM27.238 28.6411H15.3383L4.90227 17.5778C4.31446 16.9885 4.31446 16.0326 4.90227 15.4448C5.47352 14.8765 6.38863 14.8102 7.11074 15.5126L12.2951 19.4427C12.5227 19.616 12.8272 19.6417 13.0867 19.5182C13.3453 19.3899 13.5067 19.1275 13.5067 18.8426V4.52143C13.5067 3.68942 14.1837 3.01408 15.0141 3.01408C15.8448 3.01408 16.5215 3.68942 16.5215 4.52143L16.5217 15.8276C16.5217 16.2437 16.8595 16.5813 17.2754 16.5813C17.6915 16.5813 18.0291 16.2437 18.0291 15.8276C18.0291 14.9956 18.706 14.3202 19.5364 14.3202C20.3672 14.3202 21.0438 14.9956 21.0438 15.8276V17.335C21.0438 17.7511 21.3816 18.0886 21.7975 18.0886C22.2139 18.0889 22.5514 17.7511 22.5514 17.3352C22.5514 16.5032 23.2284 15.8279 24.0588 15.8279C24.8895 15.8279 25.5661 16.5032 25.5661 17.3352C25.5661 17.7514 25.9039 18.0889 26.3198 18.0889C26.7362 18.0889 27.074 17.7511 27.074 17.3352C27.074 16.5032 27.751 15.8279 28.5814 15.8279C29.3848 15.8279 30.0436 16.4596 30.0874 17.2509L27.238 28.6411Z"
              fill="white"></Path>
            <Path
              id="Vector_2"
              d="M21.0427 4.52116H26.7608L25.0331 6.24885C24.7392 6.54276 24.7392 7.0207 25.0331 7.31459C25.1792 7.46233 25.3722 7.53621 25.5652 7.53621C25.7581 7.53621 25.9511 7.46233 26.0988 7.31617L29.1139 4.30274C29.1833 4.23176 29.239 4.14738 29.2766 4.05695C29.3534 3.87162 29.3534 3.66499 29.2766 3.47965C29.239 3.38764 29.183 3.30483 29.1139 3.23386L26.0988 0.22043C25.8049 -0.0734766 25.327 -0.0734766 25.0331 0.22043C24.7392 0.514336 24.7392 0.992278 25.0331 1.28617L26.7608 3.01386H21.0427C20.6266 3.01386 20.2891 3.35167 20.2891 3.76754C20.2891 4.18369 20.6266 4.52116 21.0427 4.52116Z"
              fill="white"></Path>
            <Path
              id="Vector_3"
              d="M8.29319 4.52116H2.57516L4.30285 6.24885C4.59676 6.54276 4.59676 7.0207 4.30285 7.31459C4.15669 7.46233 3.96373 7.53621 3.77078 7.53621C3.57782 7.53621 3.38486 7.46233 3.23712 7.31617L0.222075 4.30274C0.152677 4.23176 0.0969383 4.14738 0.0593453 4.05695C-0.0174155 3.87162 -0.0174155 3.66499 0.0593453 3.47965C0.0969376 3.38764 0.152933 3.30483 0.222075 3.23386L3.23712 0.22043C3.53103 -0.0734766 4.00897 -0.0734766 4.30286 0.22043C4.59677 0.514336 4.59677 0.992278 4.30286 1.28617L2.57517 3.01386H8.2932C8.70935 3.01386 9.04688 3.35167 9.04688 3.76754C9.04688 4.18369 8.70933 4.52116 8.29319 4.52116Z"
              fill="white"></Path>
          </Svg>
          <Text
            style={{
              color: '#ffffffb3',
              paddingVertical: 10,
              fontFamily: 'Jost-Medium',
              fontSize: fonts._12,
              //  fontSize: RFPercentage(1.7),

              letterSpacing: 1.76,
            }}>
            Swipe to Explore More
          </Text>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  newArrivalContainer: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: 30,
    paddingBottom: 20,
  },
  exp: {
    // position: 'relative',
    // flex: 1,
    alignItems: 'center',
  },
  sliderContainer: {
    marginTop: 10,
    //  width: width * 0.85,
    width: wp('85%'),
    alignSelf: 'center',
    borderWidth: 0,
    borderColor: '#ccc',
  },
  slide: {
    width: '100%',
    borderRadius: widthPixel(20),
    overflow: 'hidden',
    paddingHorizontal: widthPixel(5),
    alignItems: 'center',
    justifyContent: 'center'
  },
  image: {
    resizeMode: 'contain',
    // height: '100%',
    height: 470,
    width: 320,
    // width: '100%',
  },
  infoContainer: {
    position: 'absolute',
    bottom: 5,
    borderBottomLeftRadius: widthPixel(18),
    borderBottomRightRadius: widthPixel(18),
    // paddingHorizontal: 25,
    alignSelf: 'center',
    width: '80%'
    // height: hp('15%'),
  },
  titleContainer: {
    flex: 1,
  },
  titleWrapper: {
    // marginHorizontal: widthPixel(5),
    // marginVertical: 10,
  },
  title: {
    color: 'white',
    textAlign: 'left',
    // fontFamily: 'Jost-Regular',
    fontFamily: fonts.FONT_FAMILY.Regular,
    fontSize: fonts._16,
    fontWeight: 500,
    lineHeight: heightPixel(22),
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    // backgroundColor: 'black',
  },
  styleNumberContainer: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: widthPixel(10),
    borderRadius: widthPixel(55),
    backgroundColor: '#ffffff14',
    // marginBottom: 10,
  },
  styleNumber: {
    fontSize: fonts._12,
    color: '#fff',
    fontFamily: fonts.FONT_FAMILY.Medium,
  },
  exploreButton: {
    paddingVertical: 8,
    paddingHorizontal: widthPixel(24),
    borderRadius: widthPixel(5),
    backgroundColor: '#ffffff14',
    borderColor: 'white',
    borderWidth: 1,
  },
  exploreButtonText: {
    fontSize: fonts._12,
    color: '#fff',
    fontFamily: fonts.FONT_FAMILY.Medium,
    fontWeight: 500,
  },
  gradientLayer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    zIndex: 2,
  },
});
