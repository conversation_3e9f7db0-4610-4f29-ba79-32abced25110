import { StyleSheet, Text, View } from 'react-native'
import { currencyHelper } from "@appmaker-xyz/shopify";
import React, { useRef } from 'react'
import { fonts, heightPixel, widthPixel } from '../../styles';

const CartSummaryTable = (props) => {

    const { cartSubTotalAmount, userData, appliedVoucher } = props;

    const jockeyBalanceMetafield = userData?.metafields?.find(
        (metafield) => metafield?.namespace === "jockey_balance"
    );

    return (
        <>

            <View style={styles.container} >

                <Text style={styles.orderDetails} >Order Details</Text>

                <View style={styles.rowContainer} >

                    <Text style={styles.subTotalText} >Subtotal</Text>
                    <Text style={styles.amount} >{
                        cartSubTotalAmount}</Text>

                </View>

                {jockeyBalanceMetafield &&
                    <View style={styles.rowContainer} >
                        <Text style={styles.subTotalText} >Customer Balance</Text>
                        <Text style={styles.amount} >{currencyHelper(
                            jockeyBalanceMetafield?.value,
                            "INR",
                        )}
                        </Text>
                    </View>
                }

                {appliedVoucher &&
                    <View style={styles.rowContainer} >
                        <Text style={styles.subTotalText} >
                            {appliedVoucher.type === 'voucher' ? 'Voucher' : 'Gift Card'} Discount
                        </Text>
                        <Text style={styles.discountAmount} >
                            - {currencyHelper(appliedVoucher.amount, "INR")}
                        </Text>
                    </View>
                }


            </View>

        </>

    )
}

export default CartSummaryTable

const styles = StyleSheet.create({
    container: {
        borderTopLeftRadius: widthPixel(16),
        borderTopRightRadius: widthPixel(16),
        padding: widthPixel(16),
        backgroundColor: '#fff',
        elevation: 8,
        shadowColor: '#000',
        shadowOpacity: 0.1,
        shadowRadius: widthPixel(10),
    },
    orderDetails: { fontSize: fonts._14, fontFamily: fonts.FONT_FAMILY.SemiBold, color: '#221f20' },
    rowContainer: { flexDirection: "row", justifyContent: "space-between", marginTop: heightPixel(8) },
    subTotalText: { fontSize: fonts._14, fontFamily: fonts.FONT_FAMILY.Regular, color: '#221f20' },
    amount: { fontSize: fonts._14, fontFamily: fonts.FONT_FAMILY.Medium, color: '#221f20' },
    discountAmount: { fontSize: fonts._14, fontFamily: fonts.FONT_FAMILY.Medium, color: '#4CAF50' }
})