import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import Ripple from 'react-native-material-ripple';
import { handleAction } from '@appmaker-xyz/react-native';
import { useCart, useUser } from '@appmaker-xyz/shopify';
import { fonts, heightPixel, widthPixel } from '../../styles';
import LinearGradient from 'react-native-linear-gradient';
import { currencyHelper } from '@appmaker-xyz/shopify';
import { appStorageApi } from '@appmaker-xyz/core';
import { getCheckoutUrl } from '../../utils/checkoutUtils';

const CheckoutButton = (props) => {
  const { openCheckout, cartTotalPayableWithCurrency } = useCart(props);
  const { isLoggedin } = useUser();

  // Function to log checkout URL and details
  const handleCheckout = async () => {
    // Get the current checkout state
    const currentCart = appStorageApi().getState().checkout;
    const checkoutUrl = getCheckoutUrl();

    // Log detailed information about the checkout
    console.log('CHECKOUT URL:', checkoutUrl);
    console.log('CHECKOUT PAYLOAD:', JSON.stringify(currentCart, null, 2));

    // If using cart API, also log that information
    const shopifyCart = appStorageApi().getState().shopifyCart;
    if (shopifyCart) {
      console.log('SHOPIFY CART:', JSON.stringify(shopifyCart, null, 2));
    }

    // Log user information if available
    const user = appStorageApi().getState().user;
    if (user) {
      console.log('USER INFO (for checkout headers):', {
        accessToken: user.accessToken ? 'Present (not shown for security)' : 'Not present',
        email: user.email,
      });
    }

    // Open checkout as normal
    await openCheckout();

    // After checkout is initiated, log any response
    setTimeout(() => {
      const updatedCart = appStorageApi().getState().checkout;
      if (updatedCart !== currentCart) {
        console.log('CHECKOUT RESPONSE (UPDATED):', JSON.stringify(updatedCart, null, 2));
      }
    }, 1000);
  };

  return (
    <View style={styles.container}>
      <View style={styles.amountContainer}>
        <Text style={styles.amountText}>{cartTotalPayableWithCurrency}</Text>
        <Text style={styles.incl}>(Incl. Of All Taxes)</Text>
      </View>

      <LinearGradient
        start={{ x: 1, y: 1 }} // Top-left corner
        end={{ x: 0, y: 0 }}
        colors={['#221f20', '#505050']}
        style={styles.button}>
        <Ripple
          onPress={() => {
            if (isLoggedin) {
              handleCheckout();
            } else {
              handleAction({
                action: 'OPEN_INAPP_PAGE',
                pageId: 'LoginOptions',
                params: {
                  isFromCart: true,
                },
              });
            }
          }}
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Text style={styles.buttonText}>PLACE ORDER</Text>
        </Ripple>
      </LinearGradient>
    </View>
  );
};

export default CheckoutButton;

const styles = StyleSheet.create({
  container: {
    paddingVertical: heightPixel(30),
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: '#fff',
    alignItems: 'center',
    borderTopWidth: 0.5,
    borderTopColor: '#cfcfcf',
  },
  amountContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  amountText: {
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Medium,
    fontWeight: '600',
  },
  incl: {
    fontSize: fonts._11,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  buttonContainer: {
    flex: 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: fonts._16,
    fontFamily: fonts.FONT_FAMILY.Regular,
  },
  button: {
    flex: 1.5,
    justifyContent: 'center',
    alignItems: 'center',
    height: heightPixel(46),
    marginHorizontal: widthPixel(20),
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: widthPixel(12),
  },
});
